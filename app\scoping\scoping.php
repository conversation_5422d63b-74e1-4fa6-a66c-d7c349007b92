<?php
?>

<div class="card mt-4">
    <div class="card-header p-2">
        <h3 class="card-title w-100">Scoping (ID: <?php  if(isset($scoping['id'])) {echo $scoping['id'];} else {echo '-';}; ?>)

            <?php if($scoping):?>
                <?php if($scoping['status'] == 'pending'):?>
                    <span class="badge bg-azure text-blue-fg float-end">
                                        <?php echo $scoping['status']; ?>
                        <?php echo count($scoping['progress_data']); ?> von X
                                    </span>
                <?php elseif($scoping['status'] == 'completed'): ?>
                    <span class="badge bg-green text-blue-fg float-end">
                                        <?php echo $scoping['status']; ?>
                                    </span>
                <?php elseif($scoping['status'] == 'failed'): ?>
                    <span class="badge bg-red text-blue-fg float-end">
                                        <?php echo $scoping['status']; ?>
                                    </span>
                <?php endif ?>
            <?php endif ?>

        </h3>
    </div>
    <div class="card-body">
        <div class="space-y">

            <?php if(isset($scoping['status']) && $scoping['status'] == 'error'):?>

                <span class="bg-orange text-orange-fg p-2">
                                    <?php echo $scoping['status_info']; ?>
                </span>

            <?php endif ?>


            <?php if($scoping): ?>

                <div class="row">
                    <div class="col-6">
                        <a target="_blank" href="/scoping/show.php?scoping_id=<?php echo $scoping['id']; ?>" class="btn btn-6 btn-primary w-100">
                            Scoping&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>

                        <a target="_blank" href="/scoping/show.php?scoping_id=<?php echo $scoping['id']; ?>&type=offer" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Angebot&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>

                        <a target="_blank" href="/scoping/show.php?scoping_id=<?php echo $scoping['id']; ?>&type=shipping_label" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Versandlabel&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>


                    </div>
                    <div class="col-6">

                        <a target="_blank" href="/scoping/download_pdf.php?scoping_id=<?php echo $scoping['id']; ?>" class="btn btn-6 btn-primary w-100">
                            Scoping PDF&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-file-type-pdf"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4" /><path d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4" /><path d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6" /><path d="M17 18h2" /><path d="M20 15h-3v6" /><path d="M11 15v6h1a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2h-1z" /></svg>
                        </a>

                        <a target="_blank" href="/scoping/download_pdf.php?scoping_id=<?php echo $scoping['id']; ?>&is_offer=1" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Angebot PDF&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-file-type-pdf"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4" /><path d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4" /><path d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6" /><path d="M17 18h2" /><path d="M20 15h-3v6" /><path d="M11 15v6h1a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2h-1z" /></svg>
                        </a>
                        <a target="_blank" href="/project/?id=<?php echo $scoping['project_id']; ?>" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Projekt&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>
                    </div>

                    <div class="col-12 mt-3">
                        <div class="accordion" id="accordion-default">
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <button class="accordion-button collapsed p-2" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-1-default" aria-expanded="false">
                                        Einstellungen
                                    </button>
                                </div>
                                <div id="collapse-1-default" class="accordion-collapse collapse" data-bs-parent="#accordion-default" style="">
                                    <div class="accordion-body p-2">

                                        <?php
                                        $textFields = [
                                            'anrede'              => 'Anrede',
                                            'intro_text_1'        => 'Intro Text',
                                            'local_seo_text_1'    => 'Local SEO Text',
                                            'maps_seo_text_1'     => 'Maps SEO Text',
                                            'seo_text_1'          => 'SEO Text',
                                            'page_speed_text_1'   => 'Page Speed Text',
                                            'ai_text_1'           => 'KI Text',
                                            'google_ads_text_1'   => 'Google Ads Text',
                                            'conclusion_text_1'   => 'Fazit Text'
                                        ];

                                        foreach ($textFields as $fieldName => $buttonTitle): ?>
                                            <form action="" method="post" style="display:inline-block; margin:5px;">
                                                <input type="hidden" name="table" value="scopings">
                                                <input type="hidden" name="id" value="<?php echo $scoping['id']; ?>">
                                                <input type="hidden" name="field" value="text_data">
                                                <input type="hidden" name="<?php echo $fieldName; ?>" value="0">
                                                <input type="hidden" name="action" value="update_merge_data">

                                                <button type="submit" class="btn btn-sm btn-outline-secondary btn-0">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icon-tabler-trash">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M4 7l16 0"></path>
                                                        <path d="M10 11l0 6"></path>
                                                        <path d="M14 11l0 6"></path>
                                                        <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>
                                                        <path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>
                                                    </svg>
                                                    <?php echo htmlspecialchars($buttonTitle); ?>
                                                </button>
                                            </form>
                                        <?php endforeach; ?>
                                        <br>
                                        <br>

                                        <form action="" method="post">
                                            <input type="hidden" name="table" value="scopings">
                                            <input type="hidden" name="fields" value="status">
                                            <input type="hidden" name="action" value="update_fields">
                                            <input type="hidden" name="id" value="<?php if(isset($scoping['id'])) { echo $scoping['id']; } ?>">
                                            <input type="hidden" name="status" value="pending">

                                            <button name="status" type="submit" class="btn btn-sm btn-outline-secondary btn-0">
                                                -> pending
                                            </button>
                                        </form>

                                        <form action="" method="post" style="display:inline-block; margin:5px;">
                                            <input type="hidden" name="table" value="scopings">
                                            <input type="hidden" name="id" value="<?php echo $scoping['id']; ?>">
                                            <input type="hidden" name="field" value="progress_data">
                                            <input type="hidden" name="is_ai_text_finished" value="0">
                                            <input type="hidden" name="action" value="update_merge_data">

                                            <button type="submit" class="btn btn-sm btn-outline-secondary btn-0">
                                                AI Text Gen -> 0
                                            </button>
                                        </form>

                                    </div>
                                </div>
                            </div>



                        </div>
                    </div>
                </div>

            <?php else: ?>
                <div>
                    <form action="" method="post" class="d-inline">
                        <input type="hidden" name="class" value="LeadManager">
                        <input type="hidden" name="method" value="addScopingByLeadId">
                        <input type="hidden" name="leadId" value="<?php echo $lead['id']; ?>">
                        <input type="hidden" name="action" value="execute">
                        <input type="hidden" name="is_reload" value="1">
                        <button class="btn btn-accent w-100 p-5">
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-wand"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 21l15 -15l-3 -3l-15 15l3 3" /><path d="M15 6l3 3" /><path d="M9 3a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" /><path d="M19 13a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" /></svg>
                            <span>
                                            Scoping erstellen
                                        </span>
                        </button>
                    </form>
                </div>
            <?php endif ?>
        </div>
    </div>
</div>
