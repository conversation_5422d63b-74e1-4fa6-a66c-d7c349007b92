<?php
?>

<div class="card mt-4">
    <div class="card-header p-2">
        <h3 class="card-title w-100">Scoping (ID: <?php  if(isset($scoping['id'])) {echo $scoping['id'];} else {echo '-';}; ?>)

            <?php if($scoping):?>
                <?php if($scoping['status'] == 'pending'):?>
                    <span class="badge bg-azure text-blue-fg float-end">
                                        <?php echo $scoping['status']; ?>
                        <?php echo count($scoping['progress_data']); ?> von X
                                    </span>
                <?php elseif($scoping['status'] == 'completed'): ?>
                    <span class="badge bg-green text-blue-fg float-end">
                                        <?php echo $scoping['status']; ?>
                                    </span>
                <?php elseif($scoping['status'] == 'failed'): ?>
                    <span class="badge bg-red text-blue-fg float-end">
                                        <?php echo $scoping['status']; ?>
                                    </span>
                <?php endif ?>
            <?php endif ?>

        </h3>
    </div>
    <div class="card-body">
        <div class="space-y">

            <?php if(isset($scoping['status']) && $scoping['status'] == 'failed'):?>

                <span class="bg-orange text-orange-fg p-2">
                                    <?php echo $scoping['status_info']; ?>
                                </span>

            <?php endif ?>

            <?php if($scoping): ?>

                <div class="row">
                    <div class="col-6">
                        <a target="_blank" href="/scoping/show.php?scoping_id=<?php echo $scoping['id']; ?>" class="btn btn-6 btn-primary w-100">
                            Scoping&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>

                        <a target="_blank" href="/scoping/show.php?scoping_id=<?php echo $scoping['id']; ?>&type=offer" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Angebot&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>

                        <a target="_blank" href="/scoping/show.php?scoping_id=<?php echo $scoping['id']; ?>&type=shipping_label" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Versandlabel&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>


                    </div>
                    <div class="col-6">

                        <a target="_blank" href="/scoping/download_pdf.php?scoping_id=<?php echo $scoping['id']; ?>" class="btn btn-6 btn-primary w-100">
                            Scoping PDF&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-file-type-pdf"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4" /><path d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4" /><path d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6" /><path d="M17 18h2" /><path d="M20 15h-3v6" /><path d="M11 15v6h1a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2h-1z" /></svg>
                        </a>

                        <a target="_blank" href="/scoping/download_pdf.php?scoping_id=<?php echo $scoping['id']; ?>&is_offer=1" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Angebot PDF&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-file-type-pdf"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4" /><path d="M5 12v-7a2 2 0 0 1 2 -2h7l5 5v4" /><path d="M5 18h1.5a1.5 1.5 0 0 0 0 -3h-1.5v6" /><path d="M17 18h2" /><path d="M20 15h-3v6" /><path d="M11 15v6h1a2 2 0 0 0 2 -2v-2a2 2 0 0 0 -2 -2h-1z" /></svg>
                        </a>
                        <a target="_blank" href="/project/?id=<?php echo $scoping['project_id']; ?>" class="btn btn-6 btn-outline-primary w-100 mt-1">
                            Projekt&nbsp;&nbsp;
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6" /><path d="M11 13l9 -9" /><path d="M15 4h5v5" /></svg>
                        </a>
                    </div>

                    <div class="col-6 mt-3">

                    </div>
                </div>

            <?php else: ?>
                <div>
                    <form action="" method="post" class="d-inline">
                        <input type="hidden" name="class" value="LeadManager">
                        <input type="hidden" name="method" value="addScopingByLeadId">
                        <input type="hidden" name="leadId" value="<?php echo $lead['id']; ?>">
                        <input type="hidden" name="action" value="execute">
                        <input type="hidden" name="is_reload" value="1">
                        <button class="btn btn-accent w-100 p-5">
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-wand"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M6 21l15 -15l-3 -3l-15 15l3 3" /><path d="M15 6l3 3" /><path d="M9 3a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" /><path d="M19 13a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" /></svg>
                            <span>
                                            Scoping erstellen
                                        </span>
                        </button>
                    </form>
                </div>
            <?php endif ?>
        </div>
    </div>
</div>
