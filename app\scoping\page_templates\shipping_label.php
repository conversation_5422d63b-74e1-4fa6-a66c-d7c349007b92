<?php
// Template variables available:
// $scopingData - Contains the scoping data from the database
// $projectData - Contains the related project data (if available)
?>

<div class="page-entry bg-front" style="position: relative">
    <style>
        .bg-front {
            background-image:url('page_templates/front_adress.jpg');

            background-position: 20px 20px;
            background-size: 80%;
            background-repeat: no-repeat;
            color: #FFF;
        }



        html, body {
            margin: 0;
            padding: 0;
            height: 99%;
        }

        .page-entry {
            width: 210mm;
            height: 148mm;
            background-color: #FFF;
            position: relative;
            page-break-after: always;
            margin: auto;
            padding: 0;
            box-shadow: #CCC 0 0 10px;
            overflow: hidden;
            box-sizing: border-box;
        }

        .page-entry:not(:last-child) {
            page-break-after: always;
        }

        .page-entry h1 {
            color: #0C665A;
        }

        .page-content {
            padding: 2cm 1cm 1cm 2.5cm;
        }

        .page-footer {
            position: absolute;
            right: 30px;
            bottom: 30px;
            color: #989898;
            font-size: 18px;
        }

        @page {
            size: A5 landscape;
            margin: 0;
            padding: 0;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .page-entry {
                width: 210mm;
                height: 146mm;
                page-break-after: always;
                margin: 0;
                padding: 0;
                box-shadow: none;
            }

            .page-wrapper {
                margin: 0;
                padding: 0;
                overflow: hidden;
                display: inline-block;
            }

            .page-entry:last-child {
                page-break-after: avoid;
            }

            .page {
                margin: 0;
                padding: 0;
                display: inline-block;
            }
        }

        .page-break {
            page-break-before: always;
            break-before: always;
        }

    </style>
    <div class="page-content">
        <br>
        <br>
        <br>
        <br>
        <br>
        <br>
        <div class="address" style="color:#0B6756;position: absolute;bottom: 100px;right: 80px; font-size: 14px;     max-width: 345px;">
            <?php
            // Vollständige Adresse des Projektes ausgeben
            if (!empty($projectData) && !empty($projectData['info_data'])):
                $infoData = $projectData['info_data'];
            ?>

                <!-- Firmenname -->
                <?php if (!empty($infoData['company'])): ?>
                    <h3><?php echo htmlspecialchars($infoData['company']); ?></h3>
                <?php elseif (!empty($infoData['name'])): ?>
                    <h3><?php echo htmlspecialchars($infoData['name']); ?></h3>
                <?php endif; ?>

                <!-- Ansprechpartner -->
                <?php if (!empty($infoData['contact_name'])): ?>
                    <div><strong>Zu Händen: <?php echo htmlspecialchars($infoData['contact_name']); ?></strong></div>
                <?php endif; ?>

                <!-- Straße und Hausnummer -->
                <?php if (!empty($infoData['street'])): ?>
                    <div><?php echo htmlspecialchars($infoData['street']); ?></div>
                <?php endif; ?>

                <!-- PLZ und Ort -->
                <?php if (!empty($infoData['zip']) || !empty($infoData['city'])): ?>
                    <div>
                        <?php if (!empty($infoData['zip'])): ?>
                            <?php echo htmlspecialchars($infoData['zip']); ?>
                        <?php endif; ?>
                        <?php if (!empty($infoData['city'])): ?>
                            <?php echo !empty($infoData['zip']) ? ' ' : ''; ?><?php echo htmlspecialchars($infoData['city']); ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

            <?php endif; ?>
        </div>

        <div class="project-id" style="color: #ebebeb;position: absolute;bottom: 33px; left: 33px"><?php echo $scopingData['id']; ?></div>

    </div>

</div>