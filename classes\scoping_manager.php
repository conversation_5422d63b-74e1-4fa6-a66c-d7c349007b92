<?php

/**
 * ScopingManager Class
 *
 * This class manages project scopings, handling the creation and management
 * of scoping entries in the scopings table.
 */
class ScopingManager
{
    /** @var DbManager */
    private $dbManager;

    /** @var DataForSeoManager */
    private $dfsManager;

    /** @var DataForSeoManager */
    private $projectManager;

    /** @var AiManager */
    private $aiManager;

    /**
     * Constructor
     *
     * @param DbManager $dbManager The database manager instance
     */
    public function __construct()
    {
        global $dbManager;
        $this->dbManager = $dbManager;

        $this->dfsManager = new DataForSeoManager( );
        $this->projectManager = new ProjectManager( );
        $this->aiManager = new AiManager( );
    }

    /**
     * Add a scoping entry by website URL
     *
     * This function finds a project with the given website URL or creates a new one,
     * then adds a scoping entry for that project.
     *
     * @param string $websiteUrl The website URL
     * @param array $options Optional options data to store (will be JSO<PERSON> encoded)
     * @param string $text Optional text data to store
     * @return array Response array with success status and scoping data
     */
    public function addScopingByWebsiteUrl(string $websiteUrl, array $options = [], string $text = ''): array
    {
        $response = ['success' => 0];

        // Validate website URL
        if (empty($websiteUrl)) {
            $response['error'] = 'Bitte geben Sie eine Website-URL an.';
            return $response;
        }

        // Ensure URL has a scheme
        $websiteUrl = WebsiteManager::ensureUrlHasScheme($websiteUrl);

        try {
            // Check if a project with this website URL already exists
            $existingProject = $this->dbManager->findByColumn('website_url', $websiteUrl, 'projects');

            // If project exists, use its ID
            if (!empty($existingProject)) {
                $projectId = $existingProject[0]['id'];
                $response['project_exists'] = true;
                $response['project_id'] = $projectId;
            } else {
                // Project doesn't exist, create a new one
                $projectManager = new ProjectManager();
                $projectResult = $projectManager->addProject($websiteUrl);
                $projectManager->generateInfoData( $projectResult['last_id'] );

                if (!$projectResult['success']) {
                    $response['error'] = 'Fehler beim Erstellen des Projekts: ' . ($projectResult['error'] ?? 'Unbekannter Fehler');
                    return $response;
                }

                $projectId = $projectResult['last_id'];
                $response['project_created'] = true;
                $response['project_id'] = $projectId;
                $response['project_data'] = $projectResult['project_data'];
            }

            // Now add a scoping entry for this project
            $scopingResult = $this->addScopingByProjectId($projectId, $options, $text);

            if (!$scopingResult['success']) {
                $response['error'] = 'Fehler beim Erstellen des Scopings: ' . ($scopingResult['error'] ?? 'Unbekannter Fehler');
                return $response;
            }

            // Merge the scoping result with our response
            $response['success'] = 1;
            $response['last_id'] = $scopingResult['last_id'];
            $response['message'] = 'Scoping erfolgreich angelegt' .
                ($response['project_created'] ? ' mit neuem Projekt.' : ' für bestehendes Projekt.');
            $response['scoping_data'] = $scopingResult['scoping_data'];

        } catch (Exception $e) {
            $response['error'] = 'Fehler: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Add a new scoping entry for a project
     *
     * @param int $projectId The project ID
     * @param array $options Optional options data to store (will be JSON encoded)
     * @param string $text Optional text data to store
     * @return array Response array with success status and scoping data
     */
    public function addScopingByProjectId($projectId, array $options = [], string $text = ''): array
    {
        $response = ['success' => 0];

        // Validate project ID
        if (empty($projectId) || !is_numeric($projectId)) {
            $response['error'] = 'Ungültige Projekt-ID.';
            return $response;
        }

        try {
            // Check if the project exists
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $response['error'] = 'Das angegebene Projekt existiert nicht.';
                return $response;
            }

            // Prepare data for insertion
            $data = [
                'project_id' => $projectId,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Add options_data if provided
            if (!empty($options)) {
                $data['options_data'] = json_encode($options);
            }

            // Add text_data if provided
            if (!empty($text)) {
                $data['text_data'] = $text;
            }

            // Insert the new scoping entry
            $lastId = $this->dbManager->insertNewRowByData('scopings', $data);
            $this->updateSecret($lastId);

            if ($lastId) {
                $response['success'] = 1;
                $response['last_id'] = $lastId;
                $response['message'] = 'Scoping erfolgreich angelegt.';
                $response['scoping_data'] = $data;
            } else {
                $response['error'] = 'Fehler beim Anlegen des Scopings.';
            }
        } catch (Exception $e) {
            $response['error'] = 'Datenbankfehler: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Process pending scoping entries
     *
     * Fetches all scoping entries with is_completed = 0 (pending)
     *
     * @param int|null $limit Optional limit for the number of entries to fetch
     * @return array Array of pending scoping entries
     */
    public function processPending($limit = null): array
    {
        try {
            // Find all pending scoping entries
            $pendingScopings = $this->dbManager->findByValues(
                ['status' => 'pending'],
                'scopings',
                'created_at ASC',
                $limit
            );

            if (empty($pendingScopings)) {
                return [];
            }

            foreach ( $pendingScopings as $pendingScoping ) {
                $this->processScopingByScopingId( $pendingScoping['id'] );
            }

            return $pendingScopings;
        } catch (Exception $e) {
            // Log error if needed
            error_log("Error in ScopingManager::processPending: " . $e->getMessage());
            return [];
        }
    }

    public function findScopingById($scopingId)
    {
        return $this->dbManager->findById( $scopingId, 'scopings' );
    }

    public function findScopingByIdAndSecret($scopingId, $secret)
    {
        $result = $this->dbManager->findByValues( ['id' => $scopingId, 'secret' => $secret], 'scopings' );

        if( empty($result) )
            return null;

        return $result[0];
    }

    public function getRenderedScopingPages($scopingId)
    {

        $files = [
            'front.php',
            'intro_1.php',
            'local_seo_1.php',
            //'local_pack_map.php',
            'local_seo_map.php',
            'local_seo_3.php',
            'seo_1.php',
            'page_speed_1.php',
            'page_speed_2.php',
            'ai_1.php',
            'google_ads_1.php',
            'conclusion_1.php',
        ];

        return $this->getRenderedPages( $scopingId , $files);
    }

    public function getRenderedOfferPages($scopingId)
    {
        $files = [
            //'front.php',
            'offer_1.php',
            'offer_4.php',
            'offer_2.php',
            'offer_3.php',
            //'front_tern.php',
            //'offer_tern_1.php',
            //'offer_tern_2.php'

        ];

        return $this->getRenderedPages( $scopingId , $files);
    }

    public function getRenderedShippingLabelPage($scopingId)
    {
        $files = [
            'shipping_label.php'
        ];

        return $this->getRenderedPages( $scopingId , $files);
    }

    public function downloadPdf($scopingId, $isOffer = false)
    {
        $scoping = $this->findScopingById($scopingId);

        if (!$scoping) {
            http_response_code(404);
            echo "Scoping nicht gefunden.";
            exit;
        }

        $project = $this->projectManager->findProjectById($scoping['project_id']);
        if (!$project) {
            http_response_code(404);
            echo "Projekt nicht gefunden.";
            exit;
        }

        $pdfFileName = $this->getScopingPdfFileName($scopingId, $isOffer);
        $pdfFilePath = $this->getScopingPdfFilePath($scopingId, $isOffer); // Stellt sicher, dass der Pfad korrekt ist

        if (file_exists($pdfFilePath)) {
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $pdfFileName . '"');
            header('Content-Length: ' . filesize($pdfFilePath));
            readfile($pdfFilePath);
            exit;
        } else {
            http_response_code(500);
            echo "PDF-Datei konnte nicht gefunden oder erstellt werden.";
            exit;
        }
    }

    /**
     * Get rendered scoping pages from templates
     *
     * Renders PHP templates in app/scoping/page_templates/ directory
     * and returns the HTML as an array
     *
     * @param int $scopingId The scoping ID to pass to templates
     * @param int|null $pageIndex Optional specific page index to render. If null, renders all pages.
     * @return array Array of rendered HTML strings
     */
    private function getRenderedPages(int $scopingId, $files): array
    {
        $renderedPages = [];
        $templatesPath = APP_PATH . 'scoping/page_templates/';

        try {
            // Validate scoping ID
            if (empty($scopingId) || !is_numeric($scopingId)) {
                error_log("ScopingManager::getRenderedScopingPages: Invalid scoping ID: " . $scopingId);
                return $renderedPages;
            }

            // Get scoping data from database
            $scoping = $this->findScopingById($scopingId);
            if (empty($scoping)) {
                error_log("ScopingManager::getRenderedScopingPages: Scoping not found for ID: " . $scopingId);
                return $renderedPages;
            }

            // Get related project data
            $project = null;
            if (!empty($scoping['project_id'])) {
                $project = $this->projectManager->findProjectById($scoping['project_id']);
            }

            // Check if the templates directory exists
            if (!is_dir($templatesPath)) {
                error_log("ScopingManager::getRenderedScopingPages: Templates directory not found: " . $templatesPath);
                return $renderedPages;
            }

            // Build template files array based on fixed files array
            $templateFiles = [];
            foreach ($files as $fileName) {
                $filePath = $templatesPath . $fileName;
                if (file_exists($filePath)) {
                    $templateFiles[] = $filePath;
                } else {
                    error_log("ScopingManager::getRenderedScopingPages: Template file not found: " . $filePath);
                }
            }

            if (empty($templateFiles)) {
                error_log("ScopingManager::getRenderedScopingPages: No valid template files found from fixed files array");
                return $renderedPages;
            }

            // Render each template
            $pageIndex = 0;
            foreach ($templateFiles as $currentTemplateIndex => $templateFile) {
                try {
                    // Start output buffering
                    ob_start();

                    // Make scoping and project data available to templates
                    $scopingData = $scoping;
                    $projectData = $project;

                    $pageIndexData = $pageIndex;

                    $positions = $this->getPositions();

                    $pageSpeedData = $this->getPageSpeedData($scoping['id']);

                    //$rankingPositions = [];
                    //$rankingPositions[] = $this->getRankingPositionDetailsByTag(  $scopingId, 'business_competitor_scoping_' . $scopingId );
                    //foreach ( $positions as $position ) {
                    //    $rankingPositions[] = $this->getRankingPositionDetailsByTag(  $scopingId, 'ranking_scoping_' . $position .'_' . $scopingId );
                    //}

                    $mapsRankingPositions = [];
                    $mapsRankingPositions[] = $this->getRankingPositionDetailsByTag(  $scopingId, 'maps_ranking_scoping_standort_' . $scopingId );
                    foreach ( $positions as $position ) {
                        $mapsRankingPositions[] = $this->getRankingPositionDetailsByTag(  $scopingId, 'maps_ranking_scoping_' . $position .'_' . $scopingId );
                    }

                    //print_r($mapsRankingPositions);
                    //exit;

                    $adsSearchItems = $this->getAdsInfoData($scoping['id']);

                    // Include the template file
                    include $templateFile;

                    // Get the rendered content
                    $renderedContent = ob_get_clean();

                    // Add to the array if content was generated
                    if (!empty(trim($renderedContent))) {
                        $renderedPages[] = $renderedContent;
                    }

                } catch (Exception $e) {
                    // Clean the output buffer in case of error
                    if (ob_get_level()) {
                        ob_end_clean();
                    }
                    error_log("ScopingManager::getRenderedScopingPages: Error rendering template " . basename($templateFile) . ": " . $e->getMessage());
                }

                $pageIndex++;
            }

        } catch (Exception $e) {
            error_log("ScopingManager::getRenderedScopingPages: General error: " . $e->getMessage());
        }

        return $renderedPages;
    }

    public function getScopingFilesByScopingId( $scopingId )
    {
        $scoping = $this->findScopingById( $scopingId );

        if( ! $scoping )
            return;

        if( ! $this->isProgressStepFinished( $scopingId ,'is_pdf_generation_finished' ) )
            return;

        $scopingFilePath = $this->getScopingPdfFilePath( $scopingId );
        $offerFilePath   = $this->getScopingPdfFilePath( $scopingId, true );

        $scopingFileName = $this->getScopingPdfFileName( $scopingId );
        $offerFileName   = $this->getScopingPdfFileName( $scopingId, true );

        if( ! is_file( $scopingFilePath ) )
            return;

        if( ! is_file( $offerFilePath ) )
            return;

        $out = [];
        $out[] = ['file_name' => $scopingFileName, 'file_path' => $scopingFilePath ];
        $out[] = ['file_name' => $offerFileName,   'file_path' => $offerFilePath ];

        return $out;

    }

    private function processScopingByScopingId( $scopingId )
    {
        $scoping = $this->findScopingById( $scopingId );

        if( ! $scoping )
            return null;

        //DFS business data ziehen anhand info_data
        $this->handleDfsBusinessDataByScoping( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_dfs_business_data_finished' ))
            return;

        //competitors ziehen:
        $this->handleBusinessCompetitors( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_dfs_competitor_business_data_finished' ))
            return;

        //competitors anlegen:
        $this->handleAddBusinessCompetitors( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_add_business_competitor_finished' ))
            return;

        //Ads check:
        $this->handleDfsAdvertisersDataByScoping( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_dfs_ads_advertisers_finished' ))
            return;

        //Local Ranking Positionen:
        //$this->handleRankingPositions( $scoping );
//
        //if( ! $this->isProgressStepFinished( $scoping['id'], 'is_handle_ranking_positions_finished' ))
        //    return;

        $this->handleMapsRankingPositions( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_handle_maps_ranking_positions_finished' ))
            return;

        //Performance check:
        $this->handlePageSpeed( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_page_speed_finished' ))
            return;

        //KI Texte generieren
        $this->handleGenerateAiText( $scoping );

        if( ! $this->isProgressStepFinished( $scoping['id'], 'is_ai_text_finished' ))
            return;

        //PDF Generation
        $this->handlePDFGeneration( $scoping );

        if( $this->isProgressStepFinished( $scoping['id'], 'is_pdf_generation_finished' )) {
            $this->setStatus( $scopingId , 'completed' , 'Scoping abgeschlossen!');
        }

    }

    //HANDLER #######################################

    private function handleGenerateAiText($scoping)
    {
        $scopingId = $scoping['id'];
        $progress = 'is_ai_text_finished';

        $project = $this->findProjectById($scoping['project_id']);

        if(!$project) {
            $this->setStatus( $scopingId , 'error' , 'handleGenerateAiText - Konnte project nicht finden!');
            return;
        }

        $projectData = $project['info_data'];
        unset($projectData['summary']);

        $websiteHtml = $scoping['website_html'];
        if( ! $websiteHtml ) {
            $websiteManager = new WebsiteManager($this->aiManager);
            $websiteManager->analyzeWebsite( $project['website_url'] );
            $websiteHtml = $websiteManager->getHtml();

            $this->updateWebsiteHtml( $scopingId, $websiteHtml );
        }else {
            $websiteManager = new WebsiteManager($this->aiManager);
            $websiteManager->setUrl( $project['website_url'] );
            $websiteManager->setHtml( $websiteHtml );
        }

        $websiteFullText = $websiteManager->getFullText();

        if(!$websiteFullText) {
            $this->setStatus( $scopingId , 'error' , 'handleGenerateAiText - Konnte webseite nicht abrufen!');
            return;
        }

        $this->aiManager->setDefaultProvider('openrouter');
        $this->aiManager->setModel('google/gemini-2.5-flash'); //'microsoft/mai-ds-r1:free' //'mistralai/devstral-small:free'

        $basePrompt = 'Keine Handlungsempfehlungen auf deutsch im HTML Format. Nur HTML Formatierungen verwenden um es in eine Seite anzuzeigen, keine style, body, h1, css Tags usw, die zerschiessen nämlich das Layout!! Erwähne relevante Beispiele. Ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Local SEO Scoping kritisch ist! Kein Fazit. Beim verwenden von Beispielen die html verwenden <strong> Tag nutzen. Keine ungeschlossenen HTML Tags ausgeben. KEINE h1-h6 zur Formatierung verwenden!!!!';

        $businessData = $this->getBusinessDataItems($scopingId);

        //Rankings Local Pack:
        $positions = $this->getPositions();
        $rankingPositionsLocalPack = [];
        $rankingPositionsLocalPack['standort'] = $this->getRankingPositionByTag(  $scopingId,'business_competitor_scoping_' . $scopingId );
        foreach($positions as $position) {
            $rankingPositionsLocalPack[$position] = $this->getRankingPositionByTag(  $scopingId, 'ranking_scoping_'.$position.'_' . $scopingId );
        }

        $rankingPositionsMaps = [];
        $rankingPositionsMaps['standort'] = $this->getRankingPositionByTag(  $scopingId,'maps_ranking_scoping_standort_' . $scopingId );
        foreach($positions as $position) {
            $rankingPositionsMaps[$position] = $this->getRankingPositionByTag(  $scopingId, 'maps_ranking_scoping_'.$position.'_' . $scopingId );
        }

        $ratingsCompetitors = $this->getRatingsDataByTag( $scopingId,'business_competitor_scoping_' . $scopingId);

        //$this->saveText($scopingId, 'local_seo_text_1','');

        if( ! $this->isTextWritten( $scoping , 'anrede' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Gebe eine optimale Anrede für eine Email aus mit komma am Ende. Nur die Anrede ausgeben! Keine Formatierung! keine Extra Zeichen!' );


            if( $text ){
                $this->saveText( $scopingId , 'anrede' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte anrede nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'anrede' ) )
            return;

        //Local SEO Text 1, Wettbewerber, Rankings besorgen, Map ausgeben
        if( ! $this->isTextWritten( $scoping , 'local_seo_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'GOOGLE BUSINESS DATA PROJEKT:'.PHP_EOL;
            $prompt .= print_r($businessData, true);

            //$prompt .= 'RANKING für Suchbegriff "Zahnarzt" im google Local Pack an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            //$prompt .= print_r($rankingPositionsLocalPack, true);

            $prompt .= 'RANKING für Suchbegriff "Zahnarzt" in google Maps an verschiedenen Punkten im Radius von ca. 500m Entfernung. RANKINGS IN DEN GOOGLE BUSINESS DATEN IGNORIEREN!'.PHP_EOL;
            $prompt .= print_r($rankingPositionsMaps, true);

            $prompt .= 'BEWERTUNGEN VON WETTBEWERBERN:'.PHP_EOL;
            $prompt .= print_r($ratingsCompetitors, true);

            $prompt .= 'STARTSEITE DER WEBSEITE IN TEXTFORM:'.PHP_EOL;
            $prompt .= $websiteFullText;

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer Local SEO Scoping Experte und sollst ein kritisches, ausführliches Local SEO Scoping schreiben. MAXIMAL 2400 ZEICHEN! ' .$basePrompt . 'MAXIMAL 2400 ZEICHEN!' );

            $text = $this->shortenText($text, 2400);

            if( $text ){
                $this->saveText( $scopingId , 'local_seo_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte local_seo_text_1 nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'local_seo_text_1' ) )
            return;

        //if( ! $this->isTextWritten( $scoping , 'local_pack_ranking_seo_text_1' ) ) {
//
        //    $prompt = 'KONTEXT:'.PHP_EOL;
//
        //    $prompt .= 'PROJEKT:'.PHP_EOL;
        //    $prompt .= print_r($projectData, true);
//
        //    $prompt .= 'GOOGLE BUSINESS DATA:'.PHP_EOL;
        //    $prompt .= print_r($businessData, true);
//
        //    $prompt .= 'RANKING für Suchbegriff "Zahnarzt" im google Local Pack an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
        //    $prompt .= print_r($rankingPositionsLocalPack, true);
//
        //    $prompt .= 'RANKING für Suchbegriff "Zahnarzt" in google Maps an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
        //    $prompt .= print_r($rankingPositionsMaps, true);
//
        //    $prompt .= 'BEWERTUNGEN VON WETTBEWERBERN:'.PHP_EOL;
        //    $prompt .= print_r($ratingsCompetitors, true);
//
        //    $text = $this->aiManager->generateContent(
        //        $prompt,
        //        'Du bist kritischer Local SEO Scoping Experte und sollst ein kritisches, ausführliches google Local Pack Ranking Analyse schreiben. Erwähne relevante Beispiele. Erwähne die eingeblendete Map mit dargestellten Rankings aus dem Local Pack. google Vertrauen bzgl. konsistenter Daten. Ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist. Kein Fazit. Keine h1. Maximal 800 Zeichen. '  .$basePrompt );
//
//
        //    if( $text ){
        //        $this->saveText( $scopingId , 'local_pack_ranking_seo_text_1' ,$text );
        //        return;
        //    }else {
        //        //ERROR
        //        $this->setStatus( $scopingId , 'error' , 'Konnte local_pack_ranking_seo_text_1 nicht generieren!');
        //        return;
        //    }
        //}
//
        //if( ! $this->isTextWritten( $scoping , 'local_pack_ranking_seo_text_1' ) )
        //    return;

        if( ! $this->isTextWritten( $scoping , 'maps_seo_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'GOOGLE BUSINESS DATA:'.PHP_EOL;
            $prompt .= print_r($businessData, true);

            //$prompt .= 'RANKING für Suchbegriff "Zahnarzt" im google Local Pack an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            //$prompt .= print_r($rankingPositionsLocalPack, true);

            $prompt .= 'RANKING für Suchbegriff "Zahnarzt" in google Maps an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            $prompt .= print_r($rankingPositionsMaps, true);

            $prompt .= 'BEWERTUNGEN VON WETTBEWERBERN:'.PHP_EOL;
            $prompt .= print_r($ratingsCompetitors, true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer Local SEO Scoping Experte und sollst ein kritisches, google Maps SEO Scoping ohne Handlungsempfehlungen anhand des gegebenen Kontexts auf deutsch im HTML Format. erwähne relevante Beispiele. Erwähne die Positionen standort, top-left, top-right, bottom-left, bottom-right sprechend z.B. "oben links". Erwähne die eingeblendete Map mit dargestellten Rankings. Ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Local SEO Scoping kritisch ist. Kein Fazit. HTML für Formatierungen. Keine h1, kein CSS. Maximal 800 Zeichen. ' .$basePrompt );

            $text = $this->shortenText($text, 800);

            if( $text ){
                $this->saveText( $scopingId , 'maps_seo_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte maps_seo_text_1 nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'maps_seo_text_1' ) )
            return;


        //if( ! $this->isTextWritten( $scoping , 'local_seo_text_2' ) ) {
//
        //    $prompt = 'KONTEXT:'.PHP_EOL;
//
        //    $prompt .= 'EINGANGSTEXT LOKALES SEO:'.PHP_EOL;
        //    $prompt .= $this->getText($scopingId, 'local_seo_text_1');
//
        //    $prompt .= 'RANKING für Suchbegriff "Zahnarzt" im Local Pack an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
        //    $prompt .= print_r($rankingPositionsLocalPack, true);
//
        //    $prompt .= 'RANKING für Suchbegriff "Zahnarzt" in google Maps an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
        //    $prompt .= print_r($rankingPositionsMaps, true);
//
        //    $text = $this->aiManager->generateContent(
        //        $prompt,
        //        'Du bist kritischer Local SEO Scoping Experte und sollst eine kritische Ranking-Analyse im HTML Format erstellen, verweise dazu auch auf eine eingeblendete Map auf denen die Ranking Punkte im Radius von circa 500m um den Standort herum verteilt sind. Ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Local SEO Scoping kritisch ist! Kein Fazit. Keine h1, kein CSS. strong mit leerzeichen danach verwenden. Maximal 2000'. $basePrompt );
//
        //    if( $text ){
        //        $this->saveText( $scopingId , 'local_seo_text_2' ,$text );
        //        return;
        //    }else {
        //        //ERROR
        //        $this->setStatus( $scopingId , 'error' , 'Konnte local_seo_text_2 nicht generieren!');
        //        return;
        //    }
//
        //}
//
        //if( ! $this->isTextWritten( $scoping , 'local_seo_text_2' ) )
        //    return;

        //$this->saveText($scopingId, 'seo_text_1','');

        if( ! $this->isTextWritten( $scoping , 'seo_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'SEO KONTEXT STARTSEITE:'.PHP_EOL;
            $prompt .= $websiteManager->getSeoInfoAsText();

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer SEO Scoping Experte und sollst eine kritische SEO-Analyse im HTML Format erstellen, ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist! Kein Fazit. ALS HTML AUSGEBEN, Keine h1, kein CSS, MAXIMAL 2400 ZEICHEN! '.$basePrompt );

            $text = $this->shortenText($text, 2400);

            if( $text ){
                $this->saveText( $scopingId , 'seo_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte seo_text_1 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'seo_text_1' ) )
            return;


        //$this->saveText( $scopingId , 'page_speed_text_1' ,'' );

        if( ! $this->isTextWritten( $scoping , 'page_speed_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'KONTEXT Onpage-SEO und Webseite:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'seo_text_1' );

            //$prompt .= 'RANKING für Suchbegriff "Zahnarzt" im Local Pack an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            //$prompt .= print_r($rankingPositionsLocalPack, true);

            $prompt .= 'RANKING für Suchbegriff "Zahnarzt" in google Maps an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            $prompt .= print_r($rankingPositionsMaps, true);

            $prompt .= 'PERFORMANCE DATEN:'.PHP_EOL;
            $prompt .= print_r($this->getPageSpeedData($scopingId, false), true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer SEO Webseiten Pagespeed Performance Scoping Experte und sollst eine kurze kritische Pagespeed Performance Analyse im HTML Format erstellen. Verwende dazu die PERFORMANCE DATEN, ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist! Keine h1, kein CSS. Kein Fazit. Maximal 1000 Zeichen! '. $basePrompt );

            $text = $this->shortenText($text, 1000);

            if( $text ){
                $this->saveText( $scopingId , 'page_speed_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte page_speed_text_1 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'page_speed_text_1' ) )
            return;

        //$this->saveText($scopingId, 'ai_text_1', '');

        if( ! $this->isTextWritten( $scoping , 'ai_text_1' ) ) {

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'KONTEXT SEO und Webseite:'.PHP_EOL;
            $prompt .= $websiteManager->getSeoInfoAsText();

            $prompt .= 'KONTEXT LOKALE SEO BEWERTUNG:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'local_seo_text_1');

            //$prompt .= 'RANKING für Suchbegriff "Zahnarzt" im Local Pack an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            //$prompt .= print_r($rankingPositionsLocalPack, true);

            $prompt .= 'RANKING für Suchbegriff "Zahnarzt" in google Maps an verschiedenen Punkten im Radius von ca. 500m Entfernung:'.PHP_EOL;
            $prompt .= print_r($rankingPositionsMaps, true);

            $prompt .= 'KONTEXT Rich Snippets:'.PHP_EOL;
            $prompt .= $websiteManager->getAllRichSnippets() ?: "Kein LD+JSON gefunden";

            $prompt .= 'PERFORMANCE DATEN:'.PHP_EOL;
            $prompt .= print_r($this->getPageSpeedData($scopingId, false), true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer SEO Webseiten Scoping Experte für KI Suche und sollst eine kurze kritische KI-Suche Analyse erstellen. Also wie ist die potentielle Sichtbarkeit bei KIs wie ChatGPT gegeben? mit Einleitung warum KI SUCHE in Zukunft wichtig wird auch in Richtung Reputation. Verwende dazu die KONTEXT DATEN, ziehe kritische Rückschlüsse und kritische Vermutungen. Erwähne nicht das das Scoping kritisch ist! Kein Fazit. Keine h1, kein CSS. HTML für Formatierungen.' .$basePrompt );

            $text = $this->shortenText($text, 2400);

            if( $text ){
                $this->saveText( $scopingId , 'ai_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte ai_text_1 nicht generieren!');
                return;
            }

        }

        if( ! $this->isTextWritten( $scoping , 'ai_text_1' ) )
            return;


        if( ! $this->isTextWritten( $scoping , 'google_ads_text_1' ) ) {


            $adsInfo = $this->getAdsInfoData($scoping['id']);

            $prompt = 'KONTEXT:' . PHP_EOL;

            $prompt .= 'SCOPING PROJEKT:' . PHP_EOL;
            $prompt .= print_r($projectData, true);

            // Nur der erste Eintrag
            $firstAd = isset($adsInfo[0]) ? $adsInfo[0] : null;
            $prompt .= 'KONTEXT GEFUNDENE ADS FÜR DAS SCOPING PROJEKT:' . PHP_EOL;
            $prompt .= print_r($firstAd, true);

            // Restliche Einträge (ab Index 1)
            $remainingAds = array_slice($adsInfo, 1);
            $prompt .= 'WETTBEWERBER:' . PHP_EOL;
            $prompt .= print_r($remainingAds, true);

            //$prompt .= 'KONTEXT GEFUNDENE ADS:'.PHP_EOL;
            //$prompt .= print_r($this->getAdsFullDataByTag('ads_advertisers_scoping_'. $scopingId ), true);

           // print_r($prompt);
           // exit;

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist kritischer Google Ads Experte und sollst eine kurze kritische Analyse erstellen mit Einleitung warum google ADs wichtig ist. Verwende dazu die Gefundenen DATEN aus unserem System, ziehe kritische Rückschlüsse und kritische Vermutungen im Sinne des Scoping Projekts, bewerte die Ads oder kritisiere wenn keine gefunden werden. Erwähne nicht das das Scoping kritisch ist! Erwähne keine preview_images! Erwähne keine möglichen Fehler in der Ad-Erkennung! Keine Referenz zu der Kontext Struktur! Kein Fazit. Keine h1, kein CSS. HTML für Formatierungen. Maximal 1800 Zeichen!'. $basePrompt );

            $text = $this->shortenText($text, 1800);

            if( $text ){
                $this->saveText( $scopingId , 'google_ads_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte google_ads_text_1 nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'google_ads_text_1' ) )
            return;

        //$this->saveText($scopingId, 'social_media_text_1', '');

        //if( ! $this->isTextWritten( $scoping , 'social_media_text_1' ) ) {
//
        //    $prompt = 'KONTEXT:'.PHP_EOL;
//
        //    $prompt .= 'PROJEKT:'.PHP_EOL;
        //    $prompt .= print_r($projectData, true);
//
        //    $text = $this->aiManager->generateContent(
        //        $prompt,
        //        'Du bist Social Media Scoping Experte und sollst eine kurze kritische Analyse erstellen mit Einleitung warum social Media wichtig ist, insbesondere Facebook und Instagram für Reputation und Bindung. Verwende dazu die KONTEXT DATEN. Erwähne das Social Media im Rahmen eines umfangreicheren Scoping detailliert aufgenommen werden kann. Kein Fazit. HTML für Formatierungen, strong mit leerzeichen danach verwenden. Maximal 800 Zeichen!'. $basePrompt );
//
        //    if( $text ){
        //        $this->saveText( $scopingId , 'social_media_text_1' ,$text );
        //        return;
        //    }else {
        //        //ERROR
        //        $this->setStatus( $scopingId , 'error' , 'Konnte social_media_text_1 nicht generieren!');
        //        return;
        //    }
        //}
//
        //if( ! $this->isTextWritten( $scoping , 'social_media_text_1' ) )
        //    return;

        //$this->saveText($scopingId, 'intro_text_1', '');

        if( ! $this->isTextWritten( $scoping , 'intro_text_1' ) ) {

            $scopingTextData = $scoping['text_data'];
            unset($scopingTextData['social_media_text_1']);

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'ANREDE:'.PHP_EOL;
            $prompt .= $this->getText($scopingId, 'anrede');

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'TEXTE:'.PHP_EOL;
            $prompt .= print_r($scopingTextData, true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist Online Marketing Scoping Experte und sollst für die erste Seite eine kurze Einleitung verfassen. Verwende dazu die KONTEXT DATEN. Kein Fazit.'. $basePrompt );

            $text = $this->shortenText($text, 900);

            if( $text ){

                $this->saveText( $scopingId , 'intro_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte intro_text_1 nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'intro_text_1' ) )
            return;

        //$this->saveText($scopingId, 'conclusion_text_1', '');

        if( ! $this->isTextWritten( $scoping , 'conclusion_text_1' ) ) {

            $scopingTextData = $scoping['text_data'];
            unset($scopingTextData['conclusion_text_1']);
            unset($scopingTextData['intro_text_1']);
            unset($scopingTextData['social_media_text_1']);

            $prompt = 'KONTEXT:'.PHP_EOL;

            $prompt .= 'PROJEKT:'.PHP_EOL;
            $prompt .= print_r($projectData, true);

            $prompt .= 'TEXTE:'.PHP_EOL;
            $prompt .= print_r($scopingTextData, true);

            $text = $this->aiManager->generateContent(
                $prompt,
                'Du bist Online Marketing Scoping Experte und sollst ein Fazit verfassen ohne auf technische Details einzugehen mit abstrakten Empfehlungen. Verwende dazu die KONTEXT DATEN. Maximal 2400 Zeichen! '.$basePrompt );

            $text = $this->shortenText($text, 2400);

            if( $text ){
                $this->saveText( $scopingId , 'conclusion_text_1' ,$text );
                return;
            }else {
                //ERROR
                $this->setStatus( $scopingId , 'error' , 'Konnte conclusion_text_1 nicht generieren!');
                return;
            }
        }

        if( ! $this->isTextWritten( $scoping , 'conclusion_text_1' ) )
            return;


        $this->updateProgress( $scoping['id'] , $progress , 1 );
        return;


    }

    private function shortenText($text, $length)
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        $aiManager = new AiManager();
        $aiManager->setDefaultProvider('openai');
        $aiManager->setModel('gpt-4o'); // Use a capable model for structured data

        return $aiManager->generateContent(
            $text,
            "Kürze den folgenden Text auf MAXIMAL {$length} Zeichen. 
                    Behalte HTML Formatierungen bei oder ergänze sinnvoll (z. B. <strong>, <em>, <ul>, <li>). 
                    Wenn h1 bis h6 Tags enthalten sind, ersetze sie durch normalen Text sodass sie nicht als HTML-Tags ausgegeben werden!"
        );

    }


    private function getAdsInfoData($scopingId)
    {

        $scoping = $this->findScopingById($scopingId);
        $projectData = $this->findProjectById( $scoping['project_id'] );

        $adsSearchItems = [];

        // Add main project as first entry
        $adsSearchItems[] = [
            'name' => $projectData['info_data']['name'] ?? '',
            'website_url' => $projectData['website_url'] ?? '',
            'ads' => $this->getAdsSearchItemsByTag('ads_advertisers_scoping_' . $scoping['id'] ),
        ];

        // Add competitors
        $competitors = $this->projectManager->findCompetitorsByProjectId($scoping['project_id']);

        foreach( $competitors as $competitor ) {

            if( ! isset($competitor['name']) )
                continue;

            $adsSearchItems[] = [
                'name' => $competitor['info_data']['name'] ?? '',
                'website_url' => $competitor['website_url'] ?? '',
                'ads' => $this->getAdsSearchItemsByTag('ads_advertisers_competitor_scoping_' . $scoping['id'].'_'.$competitor['id'] ),
            ];
        }

        return $adsSearchItems;

    }

    private function handleDfsBusinessDataByScoping( $scoping )
    {
        $tag = 'business_data_scoping_' . $scoping['id'];
        $progress = 'is_dfs_business_data_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $task = $this->dfsManager->findTaskByTag( $tag );

        if( $task && $task['status'] == 'completed' ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }else if( $task && $task['status'] == 'failed' ) {
            $this->setStatus($scoping['id'] , 'failed', 'DFS Business Data Task Fehler!' );
        }

        //Wenn bereits vorhanden, dann abbrechen:
        if( $task )
            return;

        $project = $this->projectManager->findProjectById( $scoping['project_id'] );

        if( ! $project || ! isset($project['info_data']['company']) || ! isset($project['info_data']['city']) ) {
            $this->setStatus($scoping['id'] , 'error', 'Projekt oder Projekt info data nicht gefunden!' );
            return;
        }

        //echo $project['info_data']['name'] . ', ' . $project['info_data']['zip']. ' '. $project['info_data']['city'];
        //exit;

        $this->dfsManager->addTask(
            [
                'keyword' => $project['info_data']['name'] .', '. $project['info_data']['company'] . ', ' . $project['info_data']['zip']. ' '. $project['info_data']['city'], //"Zahnarzt",
                //'location_name' =>  $project['info_data']['city'] . ',' .'Germany', //
                //'language_code' => "de",
                "location_code" => 2276,
                'language_name' => 'German',
                'device' => "mobile",
                'tag' => $tag,
                'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "v3/business_data/google/my_business_info/task_post", '' );

    }

    private function handleDfsAdvertisersDataByScoping( $scoping )
    {

        $tag = 'ads_advertisers_scoping_' . $scoping['id'];
        $progress = 'is_dfs_ads_advertisers_finished';

        $project = $this->projectManager->findProjectById( $scoping['project_id'] );

        $competitors = $this->projectManager->getCompetitorProjects( $project['id'] );

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }


        $taskCount = 1;
        $taskCompletedFailedCount = 0;
        $task = $this->dfsManager->findTaskByTag( $tag );

        if($task && $task['status'] != 'pending') {
            $taskCompletedFailedCount++;
        }

        foreach( $competitors as $competitorProject ) {

            if( ! $competitorProject )
                continue;

            if( ! $competitorProject['website_url'] )
                continue;

            $competitorTag = 'ads_advertisers_competitor_scoping_' . $scoping['id'].'_'.$competitorProject['id'];

            $competitorTask = $this->dfsManager->findTaskByTag( $competitorTag );

            if( $competitorTask && $competitorTask['status'] != 'pending' ) {
                $taskCompletedFailedCount++;
            }

            $taskCount++;
        }

        if( $taskCount == $taskCompletedFailedCount ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }

        if( $task ) return; // Wenn schon in Bearbeitung


        if( ! $project || ! isset($project['info_data']['company']) || ! isset($project['info_data']['city']) ) {
            $this->setStatus($scoping['id'], 'error', 'Projekt oder Projekt info data nicht gefunden!');
            return;
        }

        //$keyword = $project['website_url'] . ' ' . $project['info_data']['company']. ' ' . $project['info_data']['city'] ;//['name'].' '.$project['info_data']['city']; // z. B. "Zahnarzt Mannheim"

        $this->sendAdsSearchDfsTask( $project['website_url'] , $tag );

        $competitors = $this->projectManager->getCompetitorProjects( $project['id'] );

        foreach( $competitors as $competitorProject ) {

            if( ! $competitorProject )
                continue;

            if( ! $competitorProject['website_url'] )
                continue;

            $competitorTag = 'ads_advertisers_competitor_scoping_' . $scoping['id'].'_'.$competitorProject['id'];

            $this->sendAdsSearchDfsTask( $competitorProject['website_url'] , $competitorTag );

        }

    }

    private function sendAdsSearchDfsTask( $websiteUrl , $tag )
    {
        $this->dfsManager->addTask([
            'target' => str_replace('www.','', $this->cleanUrl( $websiteUrl )),
            'location_code' => 2276, // z. B. Mannheim
            'language_code' => 'de',
            'tag' => $tag
        ]);

        $this->dfsManager->sendTasks("v3/serp/google/ads_search/task_post", 'advanced');
    }

    /**
     * @deprecated
     * @param $scoping
     * @throws Exception
     */
    private function handleDfsCompetitorDataByScoping( $scoping )
    {
        $tag = 'business_competitor_scoping_' . $scoping['id'];
        $progress = 'is_dfs_competitor_data_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $task = $this->dfsManager->findTaskByTag( $tag );

        if( $task && $task['status'] == 'completed' ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }else if( $task && $task['status'] == 'failed' ) {
            $this->setStatus($scoping['id'] , 'failed', 'DFS Competitor Task Fehler!' );
        }

        //Wenn bereits vorhanden, dann abbrechen:
        if( $task )
            return;

        $businessData = $this->dfsManager->findTaskByTag( 'business_data_scoping_' . $scoping['id'] );

        //echo '<pre>';
        //print_r( $businessData );
        //exit;

        $businessItem = $businessData['result_data'][0]['items'][0];

        if( ! $businessData || ! $businessItem ) {
            $this->setStatus($scoping['id'] , 'failed', 'DFS Competitor Task Fehler 2!' );
            return;
        }

        $this->dfsManager->addTask(
            [
                'keyword' => 'Zahnarzt', //"Zahnarzt",
                'language_code' => "de",
                'location_coordinate' => $businessItem['latitude'] . ', '.$businessItem['longitude'],
                'device' => "mobile",
                'tag' => $tag,
                //'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "/v3/serp/google/local_finder/task_post", 'advanced' );
    }

    private function handleBusinessCompetitors($scoping)
    {
        $progress = 'is_dfs_competitor_business_data_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $businessData = $this->getBusinessData( $scoping['id'] );

        if( ! isset( $businessData[0]['items'][0] ) ) {
            $this->setStatus($scoping['id'] , 'failed', 'handleBusinessCompetitors Fehler 0!' );
            return;
        }

        $businessDataItem = $businessData[0]['items'][0];

        if( ! $businessDataItem ){
            $this->setStatus($scoping['id'] , 'failed', 'handleBusinessCompetitors Fehler 1!' );
            return;
        }

        if( ! isset( $businessDataItem['people_also_search'] ) ) {
            $this->setStatus($scoping['id'] , 'failed', 'handleBusinessCompetitors Fehler 2!' );
            return;
        }

        $index = 1;
        foreach( $businessDataItem['people_also_search'] as $competitorBusiness ) {

            $tag = 'competitor_business_data_scoping_' . $scoping['id'].'_'.$index;

            $this->dfsManager->addTask(
                [
                    'keyword' => 'cid:'.$competitorBusiness['cid'], //"Zahnarzt",
                    //'location_name' =>  $project['info_data']['city'] . ',' .'Germany', //
                    //'language_code' => "de",
                    "location_code" => 2276,
                    'language_name' => 'German',
                    'device' => "mobile",
                    'tag' => $tag,
                    'se_domain' => 'google.de'
                ]
            );

            $index++;

            if( $index >= 5 )
                break;
        }

        $this->dfsManager->sendTasks( "v3/business_data/google/my_business_info/task_post", '' );

        $this->updateProgress( $scoping['id'] , $progress , 1 );
    }

    /**
     * @deprecated
     * @param $scoping
     */
    private function handleAddCompetitors( $scoping )
    {
        $progress = 'is_add_competitor_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $businessData   = $this->dfsManager->findTaskByTag('business_data_scoping_' . $scoping['id']);
        $competitorData = $this->dfsManager->findTaskByTag('business_competitor_scoping_' . $scoping['id']);

        if( ! isset($businessData['result_data'][0]['items'][0])) {
            //error
            return;
        }

        $businessItem = $businessData['result_data'][0]['items'][0];

        //echo '<pre>';
        //print_r( $businessItem );
        //exit;

        if( ! isset($competitorData['result_data'][0]['items'])) {
            return;
        }

        $competitors = $competitorData['result_data'][0]['items'];

        $addedIndex = 0;
        foreach ( $competitors as $competitor ) {

            if( $addedIndex >= 4 )
                break;

            if( $businessItem['url'] == $competitor['url'] )
                continue;

            if( ! $competitor['url'] )
                continue;

            $this->projectManager->addCompetitorProject( $competitor['url'] , $scoping['project_id'] );

            $addedIndex++;
        }

        $this->updateProgress( $scoping['id'] , $progress , 1 );

    }

    private function handleAddBusinessCompetitors($scoping)
    {
        $progress = 'is_add_business_competitor_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $competitorExistsCount          = 0;
        $competitorFinishedCount        = 0;
        $competitorBusinessDataArray    = [];
        for ( $i = 1; $i < 6; $i++ ) {

            $tag = 'competitor_business_data_scoping_' . $scoping['id'].'_'.$i;

            $competitorBusinessData = $this->dfsManager->findTaskByTag( $tag );

            if( ! $competitorBusinessData )
                continue;

            if( $competitorBusinessData['id'] ) {
                $competitorExistsCount++;
            }else {
                continue;
            }

            if( $competitorBusinessData['status'] == 'completed' || $competitorBusinessData['status'] == 'failed' ) {
                $competitorFinishedCount++;
            }

            if( $competitorBusinessData['status'] == 'completed' ) {
                $competitorBusinessDataArray[] = $competitorBusinessData['result_data'];
            }
        }

        if( $competitorExistsCount != $competitorFinishedCount )
            return;

        foreach ( $competitorBusinessDataArray as $competitor ) {

            $competitorData = $competitor[0]['items'][0];

            if( ! isset( $competitorData['url'] ) ) {
                continue;
            }

            $websiteUrl = $this->urlWithoutParams($competitorData['url']);

            if( ! $websiteUrl )
                continue;

            $competitorProjectResult = $this->projectManager->addCompetitorProject( $websiteUrl , $scoping['project_id'] );

            if( ! $competitorProjectResult['success'] )
                continue;

            if( $competitorProjectResult['last_id'] ) {

                $lat = number_format((float)$competitorData['latitude'], 5, '.', '');
                $lon = number_format((float)$competitorData['longitude'], 5, '.', '');
                $rating = $competitorData['rating']['value'] ?? null;
                $votes = $competitorData['rating']['votes_count'] ?? null;

                $infoData = [
                    'name' => $competitorData['title'],
                    'phone' => $competitorData['phone'],
                    'website' => $websiteUrl,
                    'address' => $competitorData['address'],
                    'lat' => $lat,
                    'lon' => $lon,
                    'rating' => $rating,
                    'votes' => $votes
                ];

                $this->projectManager->updateInfoData( $competitorProjectResult['last_id'] , $infoData );
                $this->projectManager->updateProjectName( $competitorProjectResult['last_id'] , $competitorData['title'] );

            }
        }

        $this->updateProgress( $scoping['id'] , $progress , 1 );
    }

    private function handleRankingPositions( $scoping )
    {
        $scopingId = $scoping['id'];
        $progress = 'is_handle_ranking_positions_finished';

        $positions = $this->getPositions();

        $completedIndex = 0;
        foreach ( $positions as $position ){

            $tag = 'ranking_scoping_'.$position.'_' . $scopingId;

            $task = $this->dfsManager->findTaskByTag( $tag );

            if( $task && $task['status'] == 'completed' ) {
                $completedIndex++;
            }else if( $task && $task['status'] == 'failed' ) {
                $this->setStatus($scoping['id'] , 'failed', 'DFS Ranking Position Fehler!' );
            }
        }

        if( $completedIndex == 4 ){
            $this->updateProgress( $scoping['id'] , $progress , 1 );
        }

        //rankings erhalten an versch. Punkten
        $businessData = $this->getBusinessDataItems( $scoping['id'] );

        $bounding = $this->getBoundingBox( $businessData['latitude'] , $businessData['longitude'], 500);

        $this->fetchRankingPosition( $scopingId , 'top-left' , $bounding['topLeft']['lat'] , $bounding['topLeft']['lon'] );
        $this->fetchRankingPosition( $scopingId , 'top-right' , $bounding['topRight']['lat'] , $bounding['topRight']['lon'] );
        $this->fetchRankingPosition( $scopingId , 'bottom-left' , $bounding['bottomLeft']['lat'] , $bounding['bottomLeft']['lon'] );
        $this->fetchRankingPosition( $scopingId , 'bottom-right' , $bounding['bottomRight']['lat'] , $bounding['bottomRight']['lon'] );
    }

    private function handleMapsRankingPositions( $scoping )
    {
        //echo 'handleMapsRankingPositions';

        $scopingId = $scoping['id'];
        $progress = 'is_handle_maps_ranking_positions_finished';

        $positions = $this->getPositions();
        $positions[] = 'standort';

        $completedIndex = 0;
        foreach ( $positions as $position ){

            $tag = 'maps_ranking_scoping_'.$position.'_' . $scopingId;

            $task = $this->dfsManager->findTaskByTag( $tag );

            if( $task && ($task['status'] == 'completed' || $task['status'] == 'failed') ) {
                $completedIndex++;
            }
            //else if( $task && $task['status'] == 'failed' ) {
            //    $this->setStatus($scoping['id'] , 'failed', 'DFS Maps Ranking Position Fehler!' );
            //}
        }

        if( $completedIndex == count($positions) ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
        }

        //rankings erhalten an versch. Punkten
        $businessData = $this->getBusinessDataItems( $scoping['id'] );

        $bounding = $this->getBoundingBox( $businessData['latitude'] , $businessData['longitude'], 500);

        $this->fetchMapsRankingPosition( $scopingId , 'standort' , $businessData['latitude'] , $businessData['longitude'] );
        $this->fetchMapsRankingPosition( $scopingId , 'top-left' , $bounding['topLeft']['lat'] , $bounding['topLeft']['lon'] );
        $this->fetchMapsRankingPosition( $scopingId , 'top-right' , $bounding['topRight']['lat'] , $bounding['topRight']['lon'] );
        $this->fetchMapsRankingPosition( $scopingId , 'bottom-left' , $bounding['bottomLeft']['lat'] , $bounding['bottomLeft']['lon'] );
        $this->fetchMapsRankingPosition( $scopingId , 'bottom-right' , $bounding['bottomRight']['lat'] , $bounding['bottomRight']['lon'] );
    }

    private function handlePageSpeed($scoping)
    {
        $progress = 'is_page_speed_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $competitors = $this->projectManager->getCompetitorProjects( $scoping['project_id'] );

        $pageSpeedManager = new PageSpeedManager();
        $projectPageSpeed = $pageSpeedManager->getLastEntry( $scoping['project_id'] );

        //get last entries of competitors and check if is_finished
        $projectCount = count($competitors) + 1;
        $finishedCount = 0;

        if( $projectPageSpeed && $projectPageSpeed['is_finished'] ) {
            $finishedCount++;
        }

        foreach( $competitors as $competitor ) {

            $pageSpeedCompetitor = $pageSpeedManager->getLastEntry( $competitor['id'] );

            if( $pageSpeedCompetitor && $pageSpeedCompetitor['is_finished'] ) {
                $finishedCount++;
            }
        }

        if( $projectCount == $finishedCount ) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
            return;
        }

        //Anlegen wenn noch nicht vorhanden:
        if( ! $projectPageSpeed ) {
            $pageSpeedManager->addEntry( $scoping['project_id'] );
        }

        foreach( $competitors as $competitor ) {

            if( ! $pageSpeedManager->getLastEntry( $competitor['id'] ) ) {
                $pageSpeedManager->addEntry( $competitor['id'] );
            }
        }
    }

    private function handlePDFGeneration($scoping)
    {
        $progress = 'is_pdf_generation_finished';

        if( $this->isProgressStepFinished( $scoping['id'] , $progress )) {
            return;
        }

        $scopingSecret = $scoping['secret'];
        if( ! $scopingSecret ) {
            $scopingSecret = $this->updateSecret($scoping['id']);
        }

        $scopingPDFUrl          = 'https://backend.kerngebiet.digital/scoping/show.php?scoping_id='.$scoping['id'].'&secret='.$scopingSecret;
        $scopingPDFOfferUrl     = 'https://backend.kerngebiet.digital/scoping/show.php?scoping_id='.$scoping['id'].'&type=offer&secret='.$scopingSecret;

        $scopingPDFFilePath         = $this->getScopingPdfFilePath($scoping['id']);
        $scopingPDFOfferFilePath    = $this->getScopingPdfFilePath($scoping['id'], true);

        $config = [
            "bestAttempt"=> true,
            'options' => [
                'format' => 'A4', // oder: Letter, Legal, Tabloid, Ledger, A3, A5
                'landscape' => false,
                'printBackground' => true,
                //'delay' => 5000,
            ],
            'waitForEvent' =>  [
                'event' => 'wait', // CRITICAL: Verify if 'networkidle0' or 'networkidle2' is supported for 'waitForEvent.event'
                // If not, use 'load' or 'domcontentloaded'.
                'timeout' => 25000
            ],
            'addScriptTag' => [
                [
                    'content' => <<<JS
                        (async () => {
                          for (let y = 0; y < document.body.scrollHeight; y += 100)
                            window.scrollTo(0, y), await new Promise(r => setTimeout(r, 100));
                        })();
                        JS
                ]
            ]
        ];

        $browserLessClient = new BrowserlessClient();
        $resultScoping = $browserLessClient->createPDF( $scopingPDFUrl , $scopingPDFFilePath , $config );

        if( ! $resultScoping['success'] ) {
            $this->setStatus($scoping['id'] , 'failed', 'Fehler beim erzeugen oder speichern der Scoping PDF '. $resultScoping['error']  );
        }

        $resultOfferScoping = $browserLessClient->createPDF( $scopingPDFOfferUrl , $scopingPDFOfferFilePath , $config );
//
        if( ! $resultOfferScoping['success'] ) {
            $this->setStatus($scoping['id'] , 'failed', 'Fehler beim erzeugen oder speichern der Scoping Offer PDF' );
        }

        if($resultScoping['success'] && $resultScoping['success']) {
            $this->updateProgress( $scoping['id'] , $progress , 1 );
        }

    }

    //GENERATE #####################################



    //HELPER #######################################

    private function getScopingPdfFilePath( $scopingId , $isOffer = false)
    {
        if( $isOffer ) {
            return BASE_PATH . 'scoping_files/'.$scopingId.'/scoping_offer_'.$scopingId.'.pdf';
        }
        else {
            return BASE_PATH . 'scoping_files/'.$scopingId.'/scoping_'.$scopingId.'.pdf';
        }
    }

    private function getScopingPdfFileName( $scopingId , $isOffer = false)
    {
        $scoping = $this->findScopingById($scopingId);

        if(!$scoping)
            return;

        $project = $this->findProjectById( $scoping['project_id'] );

        if( ! $project )
            return;

        if( ! $project['name'] )
            return;

        if( $isOffer ) {
            return $this->generatePdfFilename( 'angebot_' . $project['name'] );
        }
        else {
            return $this->generatePdfFilename( 'scoping_' . $project['name'] );
        }
    }

    function generatePdfFilename(string $name): string {
        // Leerzeichen durch Unterstriche ersetzen, Umlaute umwandeln, Sonderzeichen entfernen
        $filename = $name;

        // Unicode-normalisieren (optional, für bessere Portabilität)
        $filename = iconv('UTF-8', 'ASCII//TRANSLIT', $filename);

        // Alles außer Buchstaben, Zahlen und Unterstrich entfernen
        $filename = preg_replace('/[^a-zA-Z0-9_]/', '', $filename);

        // Alles klein schreiben
        $filename = strtolower($filename);

        return $filename . '.pdf';
    }

    private function getPageSpeedData($scopingId, $isIncludeMobileMetrics = true)
    {
        $scoping = $this->findScopingById($scopingId);
        $project = $this->findProjectById($scoping['project_id']);

        $pagespeedManager = new PageSpeedManager();
        //$pagespeedManager

        $competitors = $this->projectManager->findCompetitorsByProjectId( $scoping['project_id'] );
        
        $output = [];
        $main = [
            'name' => $project['info_data']['company'] .' | '.$this->cleanUrl( $project['website_url'] ),
            'mobile_performance' => $pagespeedManager->getLastMobileScore( $project['id'] ),
            'desktop_performance' => $pagespeedManager->getLastDesktopScore( $project['id'] ),
            'key_metrics_mobile' => $pagespeedManager->getLastKeyMetrics( $project['id'], 'mobile' )
        ];

        if($isIncludeMobileMetrics) {
            $main['key_metrics_mobile'] = $pagespeedManager->getLastKeyMetrics( $project['id'], 'mobile' );
        }

        $output[] = $main;

        $competitorIndex = 1;
        foreach($competitors as $competitor) {
            $entry = [
                'name' => 'Wettbewerber '.$competitorIndex . ' | '.$this->cleanUrl( $competitor['website_url'] ),
                'mobile_performance' => $pagespeedManager->getLastMobileScore( $competitor['id'] ),
                'desktop_performance' => $pagespeedManager->getLastDesktopScore( $competitor['id'] ),
            ];

            if($isIncludeMobileMetrics) {
                $entry['key_metrics_mobile'] = $pagespeedManager->getLastKeyMetrics( $competitor['id'], 'mobile' );
            }

            $output[] = $entry;

            $competitorIndex++;
        }
        //echo '<pre>';
        //print_r($output);
        //exit;

        return $output;

    }

    function cleanUrl($url) {
        $url = preg_replace('#^https?://#', '', $url); // remove scheme
        $url = parse_url($url, PHP_URL_PATH);          // keep only the path
        return trim($url, '/');                         // clean slashes
    }

    private function urlWithoutParams(string $url)
    {
        $parts = parse_url($url);

        if (!isset($parts['scheme'], $parts['host'], $parts['path'])) {
            return $url; // Fallback: gib Original zurück, wenn unvollständig
        }

        return $parts['scheme'] . '://' . $parts['host'] . $parts['path'];
    }

    private function saveText($scopingId, $key, $text)
    {
        $this->dbManager->updateMergeData( $scopingId , 'scopings' , 'text_data' , [ $key => $text ] );
    }

    private function getText($scopingId, $key)
    {
        $entry = $this->dbManager->findById( $scopingId ,  'scopings' );

        if( isset( $entry['text_data'][$key]) ) {
            return $entry['text_data'][$key];
        }

        return null;
    }

    private function getPositions()
    {
        $positions = [];
        $positions[] = 'top-left';
        $positions[] = 'top-right';
        $positions[] = 'bottom-left';
        $positions[] = 'bottom-right';

        return $positions;
    }

    private function updateWebsiteHtml($scopingId, $websiteHtml)
    {
        $this->dbManager->updateField($scopingId , 'scopings', 'website_html', $websiteHtml );
    }

    private function isTextWritten($scoping, $key)
    {
        $scoping = $this->findScopingById($scoping['id']);

        if( ! $scoping['text_data'] )
            return false;

        if( ! isset($scoping['text_data'][$key]) )
            return false;

        if( ! $scoping['text_data'][$key] )
            return false;

        return true;
    }

    /**
     * Gibt alle Items zurück
     * @param $scopingId
     * @param $position
     * @return |null
     */
    private function getRankingPositionByTag( $scopingId , $tag )
    {

        $localData      = $this->dfsManager->findTaskByTag ( $tag );
        $businessData   = $this->getBusinessDataItems($scopingId);

        //echo '<pre>';
        //print_r($localData);
        //exit;

        if( ! isset($localData['result_data'][0]['items']))
            return null;

        if( ! $businessData )
            return null;

        $localItems = $localData['result_data'][0]['items'];

        foreach ( $localItems as $localItem ) {

            if( $businessData['url'] == $localItem['url'] ) {
                return $localItem['rank_absolute'];
            }
        }

        return 'not ranked';
    }

    private function getAdsSearchItemsByTag($tag)
    {
        $adsSearchResult = $this->dfsManager->findTaskByTag($tag);

        if (!$adsSearchResult)
            return null;

        if (!isset($adsSearchResult['result_data'][0]['items']))
            return null;

        $items = $adsSearchResult['result_data'][0]['items'];

        // Nur diese Keys sollen erhalten bleiben
        $allowedKeys = [
            'type',
            'format',
            'preview_image',
            'first_shown',
            'last_shown'
        ];

        $filteredItems = array_map(function ($item) use ($allowedKeys) {
            return array_intersect_key($item, array_flip($allowedKeys));
        }, $items);

        return $filteredItems;
    }



    /**
     * Gibt alle Items zurück
     * @param $scopingId
     * @param $position
     * @return |null
     */
    private function getRankingPositionDetailsByTag( $scopingId , $tag )
    {

        $localData      = $this->dfsManager->findTaskByTag ( $tag );
        $businessData   = $this->getBusinessDataItems($scopingId);

        $callResult     = json_decode($localData['call_result'], true);

        //echo '<pre>';
        //print_r($tag);
        //print_r($localData);
        //exit;

        $coordinates    = explode(',',$callResult['tasks'][0]['data']['location_coordinate']);

        if( ! isset($localData['result_data'][0]['items']))
            return null;

        if( ! $businessData )
            return null;

        $localItems = $localData['result_data'][0]['items'];

        $out = null;
        foreach ( $localItems as $localItem ) {

            if( $businessData['url'] == $localItem['url'] ) {
                $out = [
                    'rank_absolute' => $localItem['rank_absolute'],
                    'lat' => trim($coordinates[0]),
                    'lon' => trim($coordinates[1])
                ];
            }
        }

        if( ! $out ) {
            $out = [
                'rank_absolute' => 99,
                'lat' => trim($coordinates[0]),
                'lon' => trim($coordinates[1])
            ];
        }

        return $out;
    }

    private function getRatingsDataByTag( $scopingId , $tag )
    {

        $localData      = $this->dfsManager->findTaskByTag ( $tag );
        $businessData   = $this->getBusinessDataItems($scopingId);

        if( ! isset($localData['result_data'][0]['items']))
            return null;

        if( ! $businessData )
            return null;

        $localItems = $localData['result_data'][0]['items'];

        return $localItems;
    }

    private function getAdsFullDataByTag(  $tag )
    {
        $adsData      = $this->dfsManager->findTaskByTag ( $tag );

        if( ! $adsData || $adsData['result_data'] )
            return '';

        return $adsData['result_data'];
    }

    private function getBusinessDataItems($scopingId )
    {
        $businessData   = $this->dfsManager->findTaskByTag('business_data_scoping_' . $scopingId );

        if( ! isset($businessData['result_data'][0]['items'][0])) {
            //error
            return null;
        }

        $businessItem = $businessData['result_data'][0]['items'][0];

        unset($businessItem['rank_group'], $businessItem['rank_absolute'], $businessItem['position']);

        return $businessItem;

    }

    private function getBusinessData($scopingId )
    {
        $businessData   = $this->dfsManager->findTaskByTag('business_data_scoping_' . $scopingId );

        if( ! isset($businessData['result_data'])) {
            //error
            return null;
        }

        $businessItem = $businessData['result_data'];

        return $businessItem;

    }

    private function setStatus( $scopingId , $status, $statusInfo = '' )
    {
        $this->dbManager->updateField($scopingId , 'scopings' , 'status', $status );

        if( $statusInfo ) {
            $this->dbManager->updateField($scopingId , 'scopings' , 'status_info', $statusInfo );
        }
    }

    private function findProjectById( $projectId )
    {
        return $this->projectManager->findProjectById( $projectId );
    }

    private function isProgressStepFinished( $scopingId , $stepName )
    {
        $scoping = $this->findScopingById( $scopingId );

        if( ! $scoping )
            return null;

        if( isset($scoping['progress_data'][$stepName]) && $scoping['progress_data'][$stepName] )
            return true;

        return false;
    }

    private function updateProgress( $scopingId, $key , $value)
    {
        $this->dbManager->updateMergeData( $scopingId , 'scopings' , 'progress_data', [ $key => $value ] );
    }

    /**
     * Updates or generates a new access control secret for a given scoping ID.
     *
     * @param string|int $scopingId
     * @return string The newly generated secret
     */
    function updateSecret($scopingId): string {
        // Beispiel: UUID als Secret generieren
        $secret = bin2hex(random_bytes(16)); // 32-stelliges Hex, sicher für Token

        $this->dbManager->updateField($scopingId, 'scopings', 'secret', $secret);

        return $secret;
    }


    /**
     * Berechnet die Koordinaten eines umschließenden Rechtecks (Bounding Box)
     * um einen gegebenen Mittelpunkt mit einer bestimmten Entfernung zu den Seiten.
     *
     * @param float $centerLat Breitengrad des Mittelpunkts in Dezimalgrad.
     * @param float $centerLon Längengrad des Mittelpunkts in Dezimalgrad.
     * @param float $distanceM Entfernung vom Mittelpunkt zu jeder der vier Seiten des Rechtecks in Metern.
     *                         Das resultierende Quadrat hat also eine Seitenlänge von 2 * $distanceM.
     * @return array Ein assoziatives Array mit vier Punkten: 'topLeft', 'topRight', 'bottomLeft', 'bottomRight'.
     *               Jeder Punkt ist ein assoziatives Array mit 'lat' und 'lon'.
     *               Gibt false zurück bei ungültigen Eingaben (z.B. negative Distanz).
     */
    private function getBoundingBox(float $centerLat, float $centerLon, float $distanceM)
    {
        if ($distanceM < 0) {
            // Oder eine Exception werfen: throw new InvalidArgumentException("Distance must be non-negative.");
            return false;
        }
        if ($centerLat < -90 || $centerLat > 90 || $centerLon < -180 || $centerLon > 180) {
            // Oder eine Exception werfen
            return false;
        }

        // Erdradius in Metern (Durchschnittswert)
        // Für höhere Genauigkeit könnte man das WGS84 Ellipsoid verwenden,
        // aber für die meisten Anwendungsfälle ist ein Kugelmodell ausreichend.
        $earthRadiusM = 6371000.0;

        // Umrechnung des Breitengrads in Radiant für trigonometrische Funktionen
        $centerLatRad = deg2rad($centerLat);

        // Berechnung der Änderung im Breitengrad (delta Latitude)
        // Die Distanz pro Breitengrad ist relativ konstant.
        // angular_distance = distance / radius
        $deltaLatRad = $distanceM / $earthRadiusM;
        $deltaLatDeg = rad2deg($deltaLatRad);

        // Berechnung der Änderung im Längengrad (delta Longitude)
        // Die Distanz pro Längengrad hängt vom Breitengrad ab (konvergiert zu den Polen).
        // distance_lon = radius_at_latitude * delta_lon_rad
        // radius_at_latitude = earth_radius * cos(center_lat_rad)
        // delta_lon_rad = distance_lon / (earth_radius * cos(center_lat_rad))

        // Vermeide Division durch Null an den Polen (cos(pi/2) = 0)
        $cosLat = cos($centerLatRad);
        if (abs($cosLat) < 1e-9) { // Nahe oder an einem Pol
            // An den Polen selbst ist ein "Quadrat" in Längengraden nicht wohldefiniert.
            // Man könnte hier deltaLonDeg auf 180 setzen, um alle Längengrade abzudecken,
            // oder einen Fehler werfen, je nach Anwendungsfall.
            // Für ein kleines Quadrat "um" den Pol werden die Längengrade sehr stark gedehnt.
            // Ein deltaLonDeg von 180 bedeutet, dass die Box von -180 bis +180 Längengrad reicht.
            $deltaLonDeg = 180.0;
        } else {
            $deltaLonRad = $distanceM / ($earthRadiusM * $cosLat);
            $deltaLonDeg = rad2deg($deltaLonRad);
        }

        // Berechne die Koordinaten der Eckpunkte
        // Norden ist +Lat, Süden ist -Lat
        // Osten ist +Lon, Westen ist -Lon

        $points = [
            'topLeft' => [
                'lat' => $centerLat + $deltaLatDeg,
                'lon' => $centerLon - $deltaLonDeg
            ],
            'topRight' => [
                'lat' => $centerLat + $deltaLatDeg,
                'lon' => $centerLon + $deltaLonDeg
            ],
            'bottomLeft' => [
                'lat' => $centerLat - $deltaLatDeg,
                'lon' => $centerLon - $deltaLonDeg
            ],
            'bottomRight' => [
                'lat' => $centerLat - $deltaLatDeg,
                'lon' => $centerLon + $deltaLonDeg
            ]
        ];

        // Optionale Normalisierung der Koordinaten:
        // Breitengrade auf [-90, 90] beschränken.
        // Längengrade auf [-180, 180] wickeln (modulo).
        // Dies ist oft nicht nötig, da viele Kartenbibliotheken dies intern handhaben
        // oder Koordinaten außerhalb des Bereichs korrekt interpretieren.
        // Beispiel für Normalisierung (optional einfügen):
        /*
        foreach ($points as &$point) {
            $point['lat'] = max(-90.0, min(90.0, $point['lat']));
            // Längengrad Normalisierung: ($lon + 180) % 360 - 180
            // Vorsicht mit fmod bei negativen Zahlen, ggf. eigene Funktion
            while ($point['lon'] <= -180) {
                $point['lon'] += 360;
            }
            while ($point['lon'] > 180) {
                $point['lon'] -= 360;
            }
        }
        unset($point); // Referenz aufheben
        */

        // Runde die Koordinaten auf 5 Nachkommastellen
        foreach ($points as &$point) {
            $point['lat'] = number_format((float)$point['lat'], 5, '.', '');
            $point['lon'] = number_format((float)$point['lon'], 5, '.', '');
        }
        unset($point); // Gute Praxis

        return $points;
    }

    private function fetchRankingPosition( $scopingId , $position , $lat , $lon )
    {
        $tag = 'ranking_scoping_'.$position.'_' . $scopingId;

        //Wenn bereits vorhanden, dann abbrechen:
        $task = $this->dfsManager->findTaskByTag( $tag );
        if( $task )
            return;

        $this->dfsManager->addTask(
            [
                'keyword' => 'Zahnarzt', //"Zahnarzt",
                'language_code' => "de",
                'location_coordinate' => $lat . ', ' . $lon,
                'device' => "mobile",
                'tag' => $tag,
                //'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "/v3/serp/google/local_finder/task_post", 'advanced' );

    }

    private function fetchMapsRankingPosition( $scopingId , $position , $lat , $lon )
    {
        $tag = 'maps_ranking_scoping_'.$position.'_' . $scopingId;

        //Wenn bereits vorhanden, dann abbrechen:
        $task = $this->dfsManager->findTaskByTag( $tag );
        if( $task )
            return;

        $this->dfsManager->addTask(
            [
                'keyword' => 'Zahnarzt', //"Zahnarzt",
                'language_code' => "de",
                'location_coordinate' => $lat . ', ' . $lon,
                'device' => "mobile",
                'tag' => $tag,
                'search_this_area' => false
                //'se_domain' => 'google.de'
            ]
        );

        $this->dfsManager->sendTasks( "/v3/serp/google/maps/task_post", 'advanced' );

    }


}
