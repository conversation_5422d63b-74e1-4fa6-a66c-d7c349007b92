<?php

// <PERSON><PERSON><PERSON>, dass WebsiteCrawler geladen ist (z.B. durch require_once)
// require_once 'WebsiteCrawler.php';

class ScrapingAntCrawler extends WebsiteCrawler
{
    /**
     * Ihr ScrapingAnt API Key.
     * @var string|null
     */
    private $apiKey;

    /**
     * Zusätzliche ScrapingAnt API Optionen.
     * @var array
     */
    private $apiOptions = [];

    /**
     * Konstruktor für ScrapingAntCrawler.
     *
     * @param string $apiKey Ihr ScrapingAnt API-Schlüssel.
     * @param string|null $url Die zu crawlende URL.
     * @param string|null $domain Die Domain, falls die URL relativ ist (optional).
     * @param array $apiOptions Zusätzliche Optionen für die ScrapingAnt API (z.B. ['proxy_country' => 'DE', 'wait_for_selector' => '#content']).
     */
    public function __construct($apiKey = null, $url = null, $domain = null)
    {
        if (empty($apiKey)) {
            // Get AI configuration from ConfigManager
            $this->apiKey = ConfigManager::get('scraping_ant')['api_token'];
        }else {
            $this->apiKey = $apiKey;
        }


        // Ruft den Konstruktor der Elternklasse auf, um die URL zu verarbeiten
        parent::__construct($url, $domain);
    }

    public function setApiOptions($apiOptions)
    {
        $this->apiOptions = $apiOptions;
    }

    public function addApiOptions($key, $value)
    {
        $this->apiOptions[$key] = $value;
    }

    /**
     * Überschreibt die fetchHtml-Methode, um die ScrapingAnt API zu verwenden.
     * Setzt $this->html und $this->statusCode.
     */
    protected function fetchHtml()
    {
        if (!$this->url) {
            $this->html = false;
            $this->statusCode = null; // Kein Ziel-URL definiert
            return;
        }

        $apiUrl = 'https://api.scrapingant.com/v2/general';
        $params = [
            'url' => $this->url,
            'x-api-key' => $this->apiKey,
            // Fügen Sie hier weitere API-Optionen hinzu
        ];

        // Fügen Sie benutzerdefinierte API-Optionen hinzu
        foreach ($this->apiOptions as $key => $value) {
            // Überschreiben Sie nicht die Kernparameter url und x-api-key
            if ($key !== 'url' && $key !== 'x-api-key') {
                $params[$key] = $value;
            }
        }

        $queryString = http_build_query($params);
        $requestUrl = $apiUrl . '?' . $queryString;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // ScrapingAnt sollte Redirects intern handhaben
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15); // Etwas längeres Timeout für API-Anfragen
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);        // Längeres Timeout für die gesamte Anfrage (JS-Rendering etc.)
        // SSL Verifizierung für die API-Verbindung sollte aktiv bleiben
        // curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        // curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_USERAGENT, 'MyApp/1.0 (PHP Wrapper for ScrapingAnt)'); // Optional: Eigener User-Agent für die API-Anfrage

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE); // Status Code der *API-Anfrage*
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            // Loggen Sie den Curl-Fehler, z.B. mit error_log()
            error_log("ScrapingAnt API Curl Error: " . $error);
            $this->html = false;
            $this->statusCode = null; // Unbekannter Status wegen Curl-Fehler
        } elseif ($httpCode >= 200 && $httpCode < 300) {
            // Erfolgreiche API-Anfrage
            $this->html = $response; // Der Response Body ist das HTML der Zielseite
            // WICHTIG: Der $httpCode hier ist der Status der *API-Anfrage* (normalerweise 200 bei Erfolg).
            // ScrapingAnt liefert den Statuscode der *Zielseite* oft nicht direkt als HTTP-Status der API-Antwort.
            // Manchmal ist er in einem Header (z.B. x-scrapingant-status-code) oder im JSON-Body, wenn man JSON-Output anfordert.
            // Für diese einfache Implementierung setzen wir den statusCode auf den API-Status.
            // Wenn Sie den *echten* Status der Zielseite brauchen, müssen Sie ggf. die ScrapingAnt-Dokumentation prüfen
            // und evtl. Header auslesen oder eine andere API-Option nutzen.
            $this->statusCode = $httpCode;
            // Versuchen Sie, den tatsächlichen Status aus einem Header zu lesen (Beispiel, Header-Name kann variieren)
            // Um Header auszulesen, müssten Sie curl_setopt($ch, CURLOPT_HEADER, true); setzen
            // und den Header-Teil von $response trennen und parsen.
            // $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            // $header = substr($response, 0, $headerSize);
            // $body = substr($response, $headerSize);
            // if (preg_match('/X-ScrapingAnt-Status-Code:\s*(\d+)/i', $header, $matches)) {
            //     $this->statusCode = (int)$matches[1];
            // }
            // $this->html = $body; // Nur den Body speichern
        } else {
            // Fehler bei der API-Anfrage (z.B. 401 Ungültiger Key, 403 Forbidden, 429 Rate Limit, 5xx Server Error)
            error_log("ScrapingAnt API Error: HTTP Status " . $httpCode . " - Response: " . substr($response, 0, 200)); // Loggen Sie den Fehler
            $this->html = false; // Kein gültiges HTML erhalten
            $this->statusCode = $httpCode; // Speichern Sie den Fehlerstatus der API
        }
    }

    /**
     * Get extended information about a URL using the ScrapingAnt extended endpoint
     *
     * @param string $url The URL to get information for
     * @param array $apiOptions Additional API options for the request
     * @return array|false Returns extended information array on success, false on failure
     */
    public function getInfoByUrl(string $url, array $apiOptions = [])
    {
        if (empty($url)) {
            error_log("ScrapingAnt getInfoByUrl: URL cannot be empty");
            return false;
        }

        $apiUrl = 'https://api.scrapingant.com/v2/general';
        $params = [
            'url' => $url,
            'x-api-key' => $this->apiKey,
            'headers' => ''
        ];

        // Add custom API options
        foreach ($apiOptions as $key => $value) {
            // Don't override core parameters url and x-api-key
            if ($key !== 'url' && $key !== 'x-api-key') {
                $params[$key] = $value;
            }
        }

        // Add default API options from the instance
        foreach ($this->apiOptions as $key => $value) {
            // Don't override parameters that were explicitly passed
            if (!isset($params[$key]) && $key !== 'url' && $key !== 'x-api-key') {
                $params[$key] = $value;
            }
        }

        $queryString = http_build_query($params);
        $requestUrl = $apiUrl . '?' . $queryString;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $requestUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_USERAGENT, 'MyApp/1.0 (PHP Wrapper for ScrapingAnt Extended)');


        $response = curl_exec($ch);

        print_r($response);

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("ScrapingAnt Extended API Curl Error: " . $error);
            return false;
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            // Successful API request - decode JSON response
            $decodedResponse = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("ScrapingAnt Extended API JSON decode error: " . json_last_error_msg());
                return false;
            }

            return $decodedResponse;
        } else {
            // API request error
            error_log("ScrapingAnt Extended API Error: HTTP Status " . $httpCode . " - Response: " . substr($response, 0, 200));
            return false;
        }
    }

    // Alle anderen Methoden (getTitle, getMetaDescription, parseContent etc.) werden von WebsiteCrawler geerbt
    // und sollten funktionieren, da fetchHtml() die $this->html und $this->statusCode Properties setzt.
}

?>