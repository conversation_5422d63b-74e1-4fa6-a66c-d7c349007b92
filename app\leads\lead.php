<?php

$info = $lead['info_data'];

?>

<div class="card <?php if (isset($lead['is_processed']) && $lead['is_processed'] ) {echo 'border-blue ';} else {echo 'border-orange text-orange';} ?> ">
    <div class="card-header">
        <div>
            <h3 class="card-title"><?php if(isset($info['name'])) {echo $info['name']; } ?></h3>
            <p class="card-subtitle"><?php if(isset($info['city'])) {echo $info['city'];} ?></p>
            <?php if(!$lead['is_processed']): ?>
                <span class="badge badge-outline text-orange">Noch nicht verarbeitet</span>
            <?php endif ?>
        </div>
        <div class="card ms-3">
            <div class="card-header p-2">
                <h3 class="card-title"><?php if(isset($leadAgent['name'])) echo $leadAgent['name']; ?><span class="card-subtitle"><?php if(isset($leadAgent['search_data']['suchbegriff'])) {echo $leadAgent['search_data']['suchbegriff'];} ?></span></h3>
            </div>
        </div>
        <div class="card-actions float-end">

            <form action="" method="post">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="overlay_template" value="delete">
                <input type="hidden" name="headline" value="Lead <span class='link-muted'><?php if(isset($info['name'])) {echo $info['name']; } ?></span> löschen?">
                <input type="hidden" name="text" value="Hiermit wird der Lead gelöscht, fortfahren?">
                <input type="hidden" name="action" value="overlay">

                <button type="submit" class="btn btn-sm btn-icon btn-outline-secondary me-5 ">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-trash"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M4 7l16 0"></path><path d="M10 11l0 6"></path><path d="M14 11l0 6"></path><path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path><path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path></svg>
                </button>

            </form>

            <form action="" method="post">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="field" value="is_archived">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="action" value="toggle">

                <button type="submit" class="btn btn-sm btn-outline-secondary btn-0 <?php if($lead['is_archived']) {echo 'd-none';} ?>">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-archive"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 4m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z" /><path d="M5 8v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-10" /><path d="M10 12l4 0" /></svg>
                    Archivieren
                </button>
                <button type="submit" class="btn btn-sm btn-outline-yellow  btn-1 <?php if(!$lead['is_archived']) {echo 'd-none';} ?>">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-archive"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 4m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z" /><path d="M5 8v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-10" /><path d="M10 12l4 0" /></svg>
                    Archiviert
                </button>
            </form>

            <form action="" method="post">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="field" value="is_email_sent">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="action" value="toggle">

                <button type="submit" class="btn btn-sm btn-outline-secondary btn-0 <?php if($lead['is_email_sent']) {echo 'd-none';} ?>">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-mail"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z" /><path d="M3 7l9 6l9 -6" /></svg>
                    Versendet?
                </button>
                <button type="submit" class="btn btn-sm btn-outline-green  btn-1 <?php if(!$lead['is_email_sent']) {echo 'd-none';} ?>">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-mail"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z" /><path d="M3 7l9 6l9 -6" /></svg>
                    Versendet!
                </button>
            </form>

            <form action="/leads/mark_called_handler.php" method="post">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="field" value="is_called">
                <input type="hidden" name="action" value="toggle">

                <button type="submit" class="btn btn-sm btn-outline-secondary btn-0 <?php if($lead['is_called']) {echo 'd-none';} ?>">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-phone"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 4h4l2 5l-2.5 1.5a11 11 0 0 0 5 5l1.5 -2.5l5 2v4a2 2 0 0 1 -2 2a16 16 0 0 1 -15 -15a2 2 0 0 1 2 -2" /></svg>
                    Angerufen?
                </button>
                <button type="submit" class="btn btn-sm btn-outline-green btn-1 <?php if(!$lead['is_called']) {echo 'd-none';} ?>">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-phone"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 4h4l2 5l-2.5 1.5a11 11 0 0 0 5 5l1.5 -2.5l5 2v4a2 2 0 0 1 -2 2a16 16 0 0 1 -15 -15a2 2 0 0 1 2 -2" /></svg>
                    Angerufen!
                </button>
            </form>

            <form method="post">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="overlay_template" value="leads/edit_lead_info_overlay">
                <input type="hidden" name="action" value="overlay">

                <button type="submit" class="btn btn-sm btn-outline-secondary">
                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-pencil"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" /><path d="M13.5 6.5l4 4" /></svg>
                    Bearbeiten
                </button>
            </form>


            <form method="post">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="overlay_template" value="leads/internal_notes_overlay">
                <input type="hidden" name="action" value="overlay">

                <?php
                // Prüfen, ob interne Notizen vorhanden sind
                $hasNotes = !empty($lead['internal_notes']);
                $buttonClass = $hasNotes ? 'btn-danger' : 'btn-outline-secondary';
                ?>

                <button type="submit" class="btn btn-sm <?php echo $buttonClass; ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-notes">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M5 3m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z" />
                        <path d="M9 7l6 0" />
                        <path d="M9 11l6 0" />
                        <path d="M9 15l4 0" />
                    </svg>
                    Notizen
                </button>



            </form>

            <form method="post">
                <input type="hidden" name="table" value="leads">
                <input type="hidden" name="id" value="<?php echo $lead['id']; ?>">
                <input type="hidden" name="overlay_template" value="leads/user_transfer_overlay">
                <input type="hidden" name="action" value="overlay">

                <button type="submit" class="btn btn-sm btn-outline-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-user-share">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0" />
                        <path d="M6 21v-2a4 4 0 0 1 4 -4h3" />
                        <path d="M16 22l5 -5" />
                        <path d="M21 21.5v-4.5h-4.5" />
                    </svg>
                    Benutzer
                </button>
            </form>



        </div>
    </div>

    <div class="card-body">
        <div class="row row-cards">
            <!-- Infos -->
            <div class="col-md-4">
                <strong class="text-primary">Infos:</strong> (ID: <?php echo $lead['id']; ?>)<br>
                <?php
                if ($info) {
                    echo "<strong class=\"text-primary\">Name:</strong> " . htmlspecialchars($info['name']) . "<br>";
                    echo "<strong class=\"text-primary\">Kontakt:</strong> " . htmlspecialchars($info['contact_name']) . "<br>";
                    echo "<strong class=\"text-primary\">Telefon:</strong> " . htmlspecialchars($info['phone']) . "<br>";
                    echo "<strong class=\"text-primary\">E-Mail:</strong> " . htmlspecialchars($info['email']) . "<br>";
                    echo "<strong class=\"text-primary\">Firma:</strong> " . htmlspecialchars($info['company']) . "<br>";
                }
                ?>
            </div>

            <!-- Über und Links -->
            <div class="col-md-4">
                <strong class="text-primary">Über:</strong><br>
                <?php
                if ($info) {
                    echo htmlspecialchars($info['about']) . "<br>";
                    echo "<strong class=\"text-primary\">Anschrift:</strong> " . htmlspecialchars($info['full_address']) . "<br>";
                    echo htmlspecialchars($info['zip'] . " " . $info['city']) . "<br>";
                }
                ?>
                <br>
                <?php

                $websiteUrl = $lead['website_url'];
                echo '<strong class="me-3 text-primary">Webseite:</strong><a class="me-3 text-decoration-underline" target="_blank" href="' . htmlspecialchars($websiteUrl) . '">'.$websiteUrl.'</a>';


                $website = $lead['website_data'];
                if ($website) {
                    $impressumUrl = $websiteCrawler->buildValidUrl( $website['imprint_url'], $lead['domain']);
                    $jobsUrl = $websiteCrawler->buildValidUrl( $website['jobs_url'], $lead['domain']);
                    $blogUrl = $websiteCrawler->buildValidUrl( $website['blog_url'], $lead['domain']);

                    if (!empty($impressumUrl)) {
                        echo '<br><strong class="me-3 text-primary">Impressum:</strong><a class="me-3 text-decoration-underline" target="_blank" href="' . htmlspecialchars($impressumUrl) . '">Impressum</a>';
                    }
                    if (!empty($jobsUrl)) {
                        echo '<br><strong class="me-3 text-primary">Jobs:</strong><a class="me-3 text-decoration-underline" target="_blank" href="' . htmlspecialchars($jobsUrl) . '">Jobs</a>';
                    }
                    if (!empty($blogUrl)) {
                        echo '<br><strong class="me-3 text-primary">Blog/News:</strong><a class="me-3 text-decoration-underline" target="_blank" href="' . htmlspecialchars($blogUrl) . '">Blog</a>';
                    }
                }
                ?>
            </div>

            <div class="col-md-4"> <!-- Bootstrap column -->
                <div class="mb-2"> <!-- mb-2 für etwas weniger Abstand als mb-3 -->
                    <label class="form-label text-primary"><strong>Erstellt am:</strong>  <?php echo htmlspecialchars($leadManager->formatDateSafe($lead['created_at'] ?? null), ENT_QUOTES); ?></label>
                </div>

                <div class="mb-2">
                    <label class="form-label text-primary"><strong>Letzte Änderung:</strong> <?php echo htmlspecialchars($leadManager->formatDateSafe($lead['updated_at'] ?? null), ENT_QUOTES); ?></label>
                </div>

                <div class="mb-2">
                    <label class="form-label text-primary"><strong>Als angerufen markiert:</strong> <?php echo htmlspecialchars($leadManager->formatDateSafe($lead['called_at'] ?? null), ENT_QUOTES); ?></label>
                </div>
                <div class="text-warning"><?php echo $lead['internal_notes']; ?></div>

                <?php
                if (isset($lead['is_processed']) && $lead['is_processed'] ) {
                    include '../scoping/scoping.php';
                }
                ?>

                <?php if ( isset($scoping['status']) && $scoping['status'] == 'completed' ): ?>
                    <form action="" method="post" class="d-inline">
                        <input type="hidden" name="class" value="LeadManager">
                        <input type="hidden" name="method" value="attachScopingFilesToEmails">
                        <input type="hidden" name="leadId" value="<?php echo $lead['id']; ?>">
                        <input type="hidden" name="action" value="execute">

                        <button type="submit" class="btn btn-outline-primary w-100 mt-2">
                            <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-paperclip"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5" /></svg>
                            Scoping an Email anhängen
                        </button>

                    </form>
                <?php endif; ?>

            </div>

            <!-- Emails -->
            <strong class="text-primary">Emails:</strong>
            <div class="col-md-12">
                <?php
                $emails = $emailManager->findEmailsByLeadId($lead['id']);
                foreach ($emails as $email) {
                    include APP_PATH . 'emails/email.php';
                }
                ?>
            </div>
        </div>
    </div>

</div>